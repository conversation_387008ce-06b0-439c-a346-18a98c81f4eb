@using System.Diagnostics.CodeAnalysis;
@using Microsoft.AspNetCore.Components;
@using Microsoft.AspNetCore.Components.Web;
@using Microsoft.JSInterop;
@using Newtonsoft.Json
@using System.Reflection
@using TEX.Model.Models;
@using TEX.ViewModel.Finished.ProductInboundLotVMs
@using TEX.ViewModel.Finished.ProductInboundRollVMs
@using TEX.ViewModel.Producttion.LotAllocateVMs

@inherits ComponentBase;

<div style="white-space: nowrap; margin:16px 0;">
    <div class="@parentTableClass">
        <Table TItem="ProductInboundLot" @bind-Items="@DetailList" ShowRefresh="false" EditDialogSize="Size.Medium"
               IsPagination="false" TableSize="TableSize.Compact" ShowSkeleton="true"   IsExcel="true"
               OnAddAsync="@OnAddAsync" OnSaveAsync="@OnSaveAsync" IsTracking="true"
               OnDeleteAsync="@OnDeleteAsync" ShowToastAfterSaveOrDeleteModel="false"
               ShowEditButton="false" ShowDeleteButton="true" 
               IsMultipleSelect="false" ShowAddButton="@isAdd"
               ShowLineNo="true" LineNoText="序号" class="footer-demo"
               ShowToolbar="true" ShowExtendButtons="true" IsBordered="true" ShowExtendDeleteButton="false"
               IsFixedHeader="true" ShowFooter="true" IsFixedFooter="true" IsHideFooterWhenNoData="true"  >
            <TableColumns>
                <TableColumn @bind-Field="@context.OrderDetailId" Text="@WtmBlazor.Localizer["_Color"]" Lookup="AllOrderDetails" />
                
                <TableColumn @bind-Field="@context.LotNo" Align="Alignment.Center">
                    <EditTemplate Context="v">
                        <BootstrapInput @bind-Value="@v.LotNo" FormatString="0" IsSelectAllTextOnFocus
                                        id="@($"lot-no-{DetailList.ToList().IndexOf(v)}")"
                                        OnEnterAsync="@((val) => OnLotEnterAsync(v, "no"))"
                                        @onkeydown="@((e) => OnKeyDown(e, "lot"))" />
                    </EditTemplate>
                </TableColumn>
                <TableColumn @bind-Field="@context.Pcs" Align="Alignment.Center">
                    <EditTemplate Context="v">
                        <BootstrapInput @bind-Value="@v.Pcs" FormatString="0" IsSelectAllTextOnFocus
                                      id="@($"lot-pcs-{DetailList.ToList().IndexOf(v)}")"
                                      OnEnterAsync="@((val) => OnLotEnterAsync(v, "pcs"))"
                                      @onkeydown="@((e) => OnKeyDown(e, "lot"))"/>
                    </EditTemplate>
                </TableColumn>
                <TableColumn @bind-Field="@context.Weight" Align="Alignment.Center">
                    <EditTemplate Context="v">
                        <BootstrapInput @bind-Value="@v.Weight" FormatString="0.##" IsSelectAllTextOnFocus
                                      id="@($"lot-weight-{DetailList.ToList().IndexOf(v)}")"
                                      OnEnterAsync="@((val) => OnLotEnterAsync(v, "weight"))"
                                      @onkeydown="@((e) => OnKeyDown(e, "lot"))"/>
                    </EditTemplate>
                </TableColumn>
                <TableColumn @bind-Field="@context.Meters" Align="Alignment.Center">
                    <EditTemplate Context="v">
                        <BootstrapInput @bind-Value="@v.Meters" FormatString="0.##" IsSelectAllTextOnFocus
                                      id="@($"lot-meters-{DetailList.ToList().IndexOf(v)}")"
                                      OnEnterAsync="@((val) => OnLotEnterAsync(v, "meters"))"
                                      @onkeydown="@((e) => OnKeyDown(e, "lot"))"/>
                    </EditTemplate>
                </TableColumn>
                <TableColumn @bind-Field="@context.Yards" Align="Alignment.Center">
                    <EditTemplate Context="v">
                        <BootstrapInput @bind-Value="@v.Yards" FormatString="0.##" IsSelectAllTextOnFocus
                                      id="@($"lot-yards-{DetailList.ToList().IndexOf(v)}")"
                                      OnEnterAsync="@((val) => OnLotEnterAsync(v, "yards"))"
                                      @onkeydown="@((e) => OnKeyDown(e, "lot"))"/>
                    </EditTemplate>
                </TableColumn>
                <TableColumn @bind-Field="@context.Location" Align="Alignment.Center">
                    <EditTemplate Context="v">
                        <BootstrapInput @bind-Value="@v.Location" FormatString="0.##" IsSelectAllTextOnFocus
                                        id="@($"lot-location-{DetailList.ToList().IndexOf(v)}")"
                                        OnEnterAsync="@((val) => OnLotEnterAsync(v, "location"))"
                                        @onkeydown="@((e) => OnKeyDown(e, "lot"))" />
                    </EditTemplate>
                </TableColumn>
                <TableColumn @bind-Field="@context.Remark" Align="Alignment.Center">
                    <EditTemplate Context="v">
                        <BootstrapInput @bind-Value="@v.Remark" FormatString="0.##" IsSelectAllTextOnFocus
                                        id="@($"lot-remark-{DetailList.ToList().IndexOf(v)}")"
                                        OnEnterAsync="@((val) => OnLotEnterAsync(v, "remark"))"
                                        @onkeydown="@((e) => OnKeyDown(e, "lot"))" />
                    </EditTemplate>
                </TableColumn>
            </TableColumns>
            <TableToolbarTemplate>
                @* <TableToolbarButton TItem="ProductInboundLot_View" Color="Color.Primary" Icon="fa-fw fa-solid fa-pen-to-square" Text="选择" OnClick="@OnSelectLotAsync" /> *@
            </TableToolbarTemplate>
            <RowButtonTemplate>
                <TableCellButton Size="Size.ExtraSmall" Icon="fa fa-info-circle" Color="Color.Info" Text="明细" OnClick="()=>OnDetailsClick(context)" />
            </RowButtonTemplate>

            <FooterTemplate>
                <TableFooterCell />
                <TableFooterCell Text="合计:" style="height: 36px;"/>
                <TableFooterCell Aggregate="AggregateType.Count" Field="@nameof(ProductInboundLot_View.LotNo)" />
                <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(ProductInboundLot_View.Pcs)" />
                <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(ProductInboundLot_View.Weight)" />
                <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(ProductInboundLot_View.Meters)" />
                <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(ProductInboundLot_View.Yards)" />
                <TableFooterCell Colspan="3" />
            </FooterTemplate>


        </Table>
    </div>
    <div class="@childTableClass">
        @*EnableKeyboardNavigationCell="true"在bb9.0以上版本有,但是并不好用,数字列中上下键依然只能+-1,无法移动焦点*@
        <Table @bind-Items="rollDetailList" TItem="ProductInboundRoll" 
               IsExcel="true" OnAddAsync="@OnAddRollAsync" OnSaveAsync="@OnSaveRollAsync" OnDeleteAsync="@OnDeleteRollAsync"
               ShowToolbar="true" ShowDeleteButton="true" ShowRefresh="false"
               ShowExtendButtons="false" IsTracking="true"
               ShowToastAfterSaveOrDeleteModel="false" TableSize="TableSize.Compact"  
               IsFixedHeader="true"  IsBordered="true"
               ShowFooter="true" IsFixedFooter="true" IsHideFooterWhenNoData="true">
            <TableColumns>
                <TableColumn @bind-Field="@context.RollNo" Align="Alignment.Center">
                    <EditTemplate Context="v">
                        <BootstrapInput @bind-Value="@v.RollNo" FormatString="0" IsSelectAllTextOnFocus
                                      id="@($"roll-rollno-{rollDetailList.ToList().IndexOf(v)}")"
                                      OnEnterAsync="@((val) => OnRollEnterAsync(v, "rollno"))"
                                      @onkeydown="@((e) => OnKeyDown(e, "roll"))"/>
                    </EditTemplate>
                </TableColumn>
                <TableColumn @bind-Field="@context.Weight" Align="Alignment.Center">
                    <EditTemplate Context="v">
                        <BootstrapInput @bind-Value="@v.Weight" FormatString="0.##" IsSelectAllTextOnFocus
                                        id="@($"roll-weight-{rollDetailList.ToList().IndexOf(v)}")"
                                        OnEnterAsync="@((val) => OnRollEnterAsync(v, "weight"))"
                                        @onkeydown="@((e) => OnKeyDown(e, "roll"))" />
                    </EditTemplate>
                </TableColumn>
                <TableColumn @bind-Field="@context.Meters" Align="Alignment.Center">
                    <EditTemplate Context="v">
                        <BootstrapInput @bind-Value="@v.Meters" FormatString="0.##" IsSelectAllTextOnFocus
                                      id="@($"roll-meters-{rollDetailList.ToList().IndexOf(v)}")"
                                      OnEnterAsync="@((val) => OnRollEnterAsync(v, "meters"))"
                                      @onkeydown="@((e) => OnKeyDown(e, "roll"))"/>
                    </EditTemplate>
                </TableColumn>
                <TableColumn @bind-Field="@context.Yards" Align="Alignment.Center">
                    <EditTemplate Context="v">
                        <BootstrapInput @bind-Value="@v.Yards" FormatString="0.##" IsSelectAllTextOnFocus
                                      id="@($"roll-yards-{rollDetailList.ToList().IndexOf(v)}")"
                                      OnEnterAsync="@((val) => OnRollEnterAsync(v, "yards"))"
                                      @onkeydown="@((e) => OnKeyDown(e, "roll"))"/>
                    </EditTemplate>
                </TableColumn>
                <TableColumn @bind-Field="@context.Remark" Align="Alignment.Center">
                    <EditTemplate Context="v">
                        <BootstrapInput @bind-Value="@v.Remark" FormatString="0.##" IsSelectAllTextOnFocus
                                        id="@($"roll-remark-{rollDetailList.ToList().IndexOf(v)}")"
                                        OnEnterAsync="@((val) => OnRollEnterAsync(v, "remark"))"
                                        @onkeydown="@((e) => OnKeyDown(e, "roll"))" />
                    </EditTemplate>
                </TableColumn>
            </TableColumns>
            <TableToolbarTemplate>
                <TableToolbarButton Color="Color.Warning" Icon="fa fa-info-save" Text="保存" OnClick="@SaveRolls" />
            </TableToolbarTemplate> 
            <FooterTemplate>
                @* <TableFooterCell Text="合计:" style="height: 36px;" /> *@
                <TableFooterCell style="height: 36px;" Text="合计:" Aggregate="AggregateType.Count" Field="@nameof(ProductInboundRoll.RollNo)" />
                <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(ProductInboundRoll.Weight)" />
                <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(ProductInboundRoll.Meters)" />
                <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(ProductInboundRoll.Yards)" />
                <TableFooterCell  />
            </FooterTemplate>
        </Table>

    </div>
</div>

<style>
    .parent-table-normal {
        width: 100%;
        display: inline-block;
    }

    .parent-table-compressed {
        width: 60%;
        display: inline-block;
        vertical-align: top;
    }

    .child-table-hidden {
        display: none;
    }

    .child-table-visible {
        width: 40%;
        display: inline-block;
        vertical-align: top;
    }

    .footer-demo hr {
        margin: 0;
    }

    .footer-demo tfoot tr,
    .footer-demo .table-row.table-footer .table-cell {
        color: #409eff;
        font-weight: bold;
    }
</style>

<script>
    // 键盘导航常量定义
    const KeyCodes = {
        LEFT_ARROW: 37,
        UP_ARROW: 38,
        RIGHT_ARROW: 39,
        DOWN_ARROW: 40,
        ENTER: 13,
        TAB: 9
    };

    // 激活指定单元格的输入框
    const activeCell = (cells, index) => {
        if (index < 0 || index >= cells.length) return false;

        const cell = cells[index];
        const input = cell.querySelector('input');

        if (input && !input.disabled && !input.readOnly) {
            input.focus();
            if (input.select) {
                input.select();
            }
            return true;
        }
        return false;
    };

    // 表格单元格导航核心函数
    const moveCell = (input, keyCode) => {
        const td = input.closest('td');
        const tr = td.parentNode;
        let cells = [...tr.children];
        let index = cells.indexOf(td);

        if (keyCode === KeyCodes.LEFT_ARROW) {
            while (--index >= 0) {
                if (activeCell(cells, index)) {
                    break;
                }
            }
        }
        else if (keyCode === KeyCodes.RIGHT_ARROW) {
            while (++index < cells.length) {
                if (activeCell(cells, index)) {
                    break;
                }
            }
        }
        else if (keyCode === KeyCodes.UP_ARROW) {
            let nextRow = tr.previousElementSibling;
            while (nextRow) {
                cells = nextRow.children;
                if (cells && activeCell(cells, index)) {
                    break;
                }
                nextRow = nextRow.previousElementSibling;
            }
        }
        else if (keyCode === KeyCodes.DOWN_ARROW) {
            let nextRow = tr.nextElementSibling;
            while (nextRow) {
                cells = nextRow.children;
                if (cells && activeCell(cells, index)) {
                    break;
                }
                nextRow = nextRow.nextElementSibling;
            }
        }
    };

    // 键盘事件处理函数
    window.handleTableKeyNavigation = function (event, tableType) {
        const keyCode = event.keyCode || event.which;
        const input = event.target;

        // 只处理方向键
        if ([KeyCodes.LEFT_ARROW, KeyCodes.UP_ARROW, KeyCodes.RIGHT_ARROW, KeyCodes.DOWN_ARROW].includes(keyCode)) {
            event.preventDefault();
            moveCell(input, keyCode);
            return false;
        }

        return true;
    };

    // 简化的焦点设置函数（保持向后兼容）
    window.focusElement = function (elementId) {
        try {
            let element = document.getElementById(elementId);

            if (!element) {
                element = document.querySelector(`[id="${elementId}"]`);
            }

            if (!element) {
                element = document.querySelector(`input[id="${elementId}"]`);
            }

            if (element) {
                element.focus();
                if (element.select) {
                    element.select();
                }
                return true;
            } else {
                console.warn(`Element with id '${elementId}' not found`);
                return false;
            }
        } catch (error) {
            console.error(`Error focusing element '${elementId}':`, error);
            return false;
        }
    };

    // Enter键导航到下一行（保持现有功能）
    window.navigateToNextRow = function (currentElementId, tableType) {
        try {
            const currentElement = document.getElementById(currentElementId) ||
                                 document.querySelector(`[id="${currentElementId}"]`) ||
                                 document.querySelector(`input[id="${currentElementId}"]`);

            if (currentElement) {
                const td = currentElement.closest('td');
                const tr = td.parentNode;
                const nextRow = tr.nextElementSibling;

                if (nextRow) {
                    const cells = [...tr.children];
                    const currentIndex = cells.indexOf(td);
                    const nextRowCells = [...nextRow.children];

                    if (nextRowCells[currentIndex]) {
                        const nextInput = nextRowCells[currentIndex].querySelector('input');
                        if (nextInput) {
                            nextInput.focus();
                            if (nextInput.select) {
                                nextInput.select();
                            }
                            return true;
                        }
                    }
                }
            }
            return false;
        } catch (error) {
            console.error(`Error navigating to next row:`, error);
            return false;
        }
    };
</script>
@code {

    [Parameter]
    public ProductInboundBill Bill { get; set; } = new();
    [Parameter]
    public EventCallback<ProductInboundBill> BillChanged { get; set; }

    [Inject]
    private WtmBlazorContext WtmBlazor { get; set; }

    [Inject]
    private IJSRuntime JSRuntime { get; set; }

    private List<SelectedItem> AllOrderDetails { get; set; } = new List<SelectedItem>();

    ProductInboundLot SelectedLot { get; set; }//存储当前选择的Lot
    private bool isAdd = false;
    //Lot子表绑定数据
    public IEnumerable<ProductInboundLot> DetailList
    {
        get { return Bill.LotList; }
        set
        {
            Bill.LotList = value.ToList();
        }
    }

    //Roll孙表绑定数据
    List<ProductInboundRoll> RollDetailList = new List<ProductInboundRoll>();
    public IEnumerable<ProductInboundRoll> rollDetailList
    {
        get { return RollDetailList; }
        set
        {
            RollDetailList = value.ToList();
        }
    }


    protected override async Task OnInitializedAsync()
    {
        if (Bill.LotList is null) Bill.LotList = new List<ProductInboundLot>();

        await base.OnInitializedAsync();
    }

    //当选择订单后,刷新订单明细选择数据源
    protected override async Task OnParametersSetAsync()
    {
        if (Bill.POrderId != Guid.Empty)
        {
            await SearchOrderDetailAsync(Bill.POrderId);
            isAdd = true;
        }
    }

    private async Task SearchOrderDetailAsync(Guid id)
    {
        var rv = await WtmBlazor.Api.CallAPI<List<SelectedItem>>($"/api/Models/OrderDetail/GetOrderDetailSelectListItemsByPurchaseOrderId/{id}");
        AllOrderDetails = rv.Data;
    }



    #region 更新Lot
    //Lot子表Excel模式,更新方法
    private async Task<ProductInboundLot> OnAddAsync()
    {
        var od = new ProductInboundLot();
        if (Bill.LotList is null) Bill.LotList = new();
        od.ID = Guid.NewGuid();
        Bill.LotList.Insert(Bill.LotList.Count(), od);
        //有async关键字就需要加await
        return await Task.FromResult(od);
        //return null;
    }

    private Task<bool> OnSaveAsync(ProductInboundLot item, ItemChangedType changedType)
    {
        Bill.Pcs = Bill.LotList.Count;
        Bill.Weight = Bill.LotList.Sum(x => x.Weight);
        Bill.Meters = Bill.LotList.Sum(x => x.Meters);
        Bill.Yards = Bill.LotList.Sum(x => x.Yards);
        return Task.FromResult(true);
    }

    private Task<bool> OnDeleteAsync(IEnumerable<ProductInboundLot> items)
    {
        Bill.LotList.RemoveAll(i => items.ToList().Contains(i));
        // 输出日志信息
        //Logger.Log($"集合值变化通知 列: {Items.Count} - 类型: Delete");
        return Task.FromResult(true);
    }
    #endregion


    #region 更新Roll
    //更新Roll
    private async Task<ProductInboundRoll> OnAddRollAsync()
    {
        var od = new ProductInboundRoll();
        if (RollDetailList is null) RollDetailList = new();
        od.ID = Guid.NewGuid();
        od.RollNo = RollDetailList.Count + 1;
        RollDetailList.Insert(RollDetailList.Count(), od);
        //有async关键字就需要加await
        return await Task.FromResult(od);
        //return null;
    }

    private Task<bool> OnSaveRollAsync(ProductInboundRoll item, ItemChangedType changedType)
    {
        // Bill.Pcs = RollDetailList.Count();
        // Bill.Weight = RollDetailList.Sum(x => x.Weight);
        // Bill.Meters = RollDetailList.Sum(x => x.Meters);
        // Bill.Yards = RollDetailList.Sum(x => x.Yards);
        return Task.FromResult(true);
    }

    private Task<bool> OnDeleteRollAsync(IEnumerable<ProductInboundRoll> items)
    {
        RollDetailList.RemoveAll(i => items.ToList().Contains(i));
        return Task.FromResult(true);
    }
    private string parentTableClass = "parent-table-normal";
    private string childTableClass = "child-table-hidden";
    //行明细按钮点击控制显示Roll表格
    private void OnDetailsClick(ProductInboundLot item)
    {
        // 检查是否点击的是同一行
        if (SelectedLot is not null && SelectedLot.ID == item.ID)
        {
            // 点击同一行时，切换显示状态
            parentTableClass = parentTableClass == "parent-table-compressed" ? "parent-table-normal" : "parent-table-compressed";
            childTableClass = childTableClass == "child-table-visible" ? "child-table-hidden" : "child-table-visible";
        }
        else
        {
            // 点击不同行时，显示新行的数据
            SelectedLot = item;
            RollDetailList = item.RollList ?? new List<ProductInboundRoll>();

            // 确保明细表格显示
            parentTableClass = "parent-table-compressed";
            childTableClass = "child-table-visible";
        }
        StateHasChanged();
    }

    //保存Roll
    private async Task SaveRolls()
    {
        var list = new List<ProductInboundRoll>();

        //去除空白Roll
        foreach (var item in RollDetailList)
        {
            if (item.Meters != 0 || item.Weight != 0 || item.Yards != 0)
            {
                list.Add(item);
            }
        }
        SelectedLot.RollList = list;
        SelectedLot.Color = AllOrderDetails.FirstOrDefault(x => x.Value == SelectedLot.OrderDetailId.ToString())?.Text;
        SelectedLot.Pcs = SelectedLot.RollList.Count;
        SelectedLot.Weight = SelectedLot.RollList.Sum(x => x.Weight);
        SelectedLot.Meters = SelectedLot.RollList.Sum(x => x.Meters);
        SelectedLot.Yards = SelectedLot.RollList.Sum(x => x.Yards);
        int index = Bill.LotList.FindIndex(x => x.ID == SelectedLot.ID);
        Bill.LotList[index] = SelectedLot;
        // 保存后隐藏明细表格
        parentTableClass = "parent-table-normal";
        childTableClass = "child-table-hidden";
        await Task.CompletedTask;
        //StateHasChanged(); //Click事件是EventCallback类型,本身会触发StateHasChanged，所以不需要再次触发
    }
    #endregion

    // 统一的键盘事件处理
    private async Task OnKeyDown(KeyboardEventArgs e, string tableType)
    {
        try
        {
            // 调用JavaScript处理方向键导航
            await JSRuntime.InvokeVoidAsync("handleTableKeyNavigation", e, tableType);
        }
        catch (Exception ex)
        {
            // 忽略键盘导航失败的错误，不影响主要功能
            System.Console.WriteLine($"Keyboard navigation failed: {ex.Message}");
        }
    }

    // Lot表格Enter键处理（简化版）
    private async Task OnLotEnterAsync(ProductInboundLot currentLot, string columnName)
    {
        var lotList = DetailList.ToList();
        var currentIndex = lotList.IndexOf(currentLot);
        var nextIndex = currentIndex + 1;

        // 如果是最后一行，自动添加新行
        if (nextIndex >= lotList.Count)
        {
            await OnAddAsync(); // 添加新行
            nextIndex = lotList.Count;
            StateHasChanged(); // 强制重新渲染，确保新行DOM元素已创建
            await Task.Delay(50); // 等待DOM更新完成
        }

        // 使用JavaScript导航到下一行
        var currentElementId = $"lot-{columnName}-{currentIndex}";
        await JSRuntime.InvokeVoidAsync("navigateToNextRow", currentElementId, "lot");
    }

    // Roll表格Enter键处理（简化版）
    private async Task OnRollEnterAsync(ProductInboundRoll currentRoll, string columnName)
    {
        var rollList = rollDetailList.ToList();
        var currentIndex = rollList.IndexOf(currentRoll);
        var nextIndex = currentIndex + 1;

        // 如果是最后一行，自动添加新行
        if (nextIndex >= rollList.Count)
        {
            await OnAddRollAsync(); // 添加新行
            nextIndex = rollList.Count;
            StateHasChanged(); // 强制重新渲染，确保新行DOM元素已创建
            await Task.Delay(100); // 等待DOM更新完成
        }

        // 使用JavaScript导航到下一行
        var currentElementId = $"roll-{columnName}-{currentIndex}";
        await JSRuntime.InvokeVoidAsync("navigateToNextRow", currentElementId, "roll");
    }

    // 通过JavaScript设置焦点（保持向后兼容）
    private async Task FocusElement(string elementId)
    {
        try
        {
            await JSRuntime.InvokeVoidAsync("focusElement", elementId);
        }
        catch (Exception ex)
        {
            // 忽略焦点设置失败的错误，不影响主要功能
            System.Console.WriteLine($"Focus failed for element {elementId}: {ex.Message}");
        }
    }
}