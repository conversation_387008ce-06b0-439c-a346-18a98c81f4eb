@using System.Diagnostics.CodeAnalysis;
@using Microsoft.AspNetCore.Components;
@using Microsoft.JSInterop;
@using Newtonsoft.Json
@using System.Reflection
@using TEX.Model.Models;
@using TEX.ViewModel.Finished.ProductInboundLotVMs
@using TEX.ViewModel.Finished.ProductInboundRollVMs
@using TEX.ViewModel.Producttion.LotAllocateVMs

@inherits ComponentBase;

<div style="white-space: nowrap; margin:16px 0;">
    <div class="@parentTableClass">
        <Table TItem="ProductInboundLot" @bind-Items="@DetailList" ShowRefresh="false" EditDialogSize="Size.Medium"
               IsPagination="false" TableSize="TableSize.Compact" ShowSkeleton="true"   IsExcel="true"
               OnAddAsync="@OnAddAsync" OnSaveAsync="@OnSaveAsync" IsTracking="true"
               OnDeleteAsync="@OnDeleteAsync" ShowToastAfterSaveOrDeleteModel="false"
               ShowEditButton="false" ShowDeleteButton="true" 
               IsMultipleSelect="false" ShowAddButton="@isAdd"
               ShowLineNo="true" LineNoText="序号" class="footer-demo"
               ShowToolbar="true" ShowExtendButtons="true" IsBordered="true" ShowExtendDeleteButton="false"
               IsFixedHeader="true" ShowFooter="true" IsFixedFooter="true" IsHideFooterWhenNoData="true"  >
            <TableColumns>
                <TableColumn @bind-Field="@context.OrderDetailId" Text="@WtmBlazor.Localizer["_Color"]" Lookup="AllOrderDetails" />
                
                <TableColumn @bind-Field="@context.LotNo" Align="Alignment.Center">
                    <EditTemplate Context="v">
                        <BootstrapInput @bind-Value="@v.LotNo" FormatString="0" IsSelectAllTextOnFocus
                                        OnEnterAsync="@((val) => OnLotEnterAsync())" />
                    </EditTemplate>
                </TableColumn>
                <TableColumn @bind-Field="@context.Pcs" Align="Alignment.Center">
                    <EditTemplate Context="v">
                        <BootstrapInput @bind-Value="@v.Pcs" FormatString="0" IsSelectAllTextOnFocus
                                      OnEnterAsync="@((val) => OnLotEnterAsync())"/>
                    </EditTemplate>
                </TableColumn>
                <TableColumn @bind-Field="@context.Weight" Align="Alignment.Center">
                    <EditTemplate Context="v">
                        <BootstrapInput @bind-Value="@v.Weight" FormatString="0.##" IsSelectAllTextOnFocus
                                      OnEnterAsync="@((val) => OnLotEnterAsync())"/>
                    </EditTemplate>
                </TableColumn>
                <TableColumn @bind-Field="@context.Meters" Align="Alignment.Center">
                    <EditTemplate Context="v">
                        <BootstrapInput @bind-Value="@v.Meters" FormatString="0.##" IsSelectAllTextOnFocus
                                      OnEnterAsync="@((val) => OnLotEnterAsync())"/>
                    </EditTemplate>
                </TableColumn>
                <TableColumn @bind-Field="@context.Yards" Align="Alignment.Center">
                    <EditTemplate Context="v">
                        <BootstrapInput @bind-Value="@v.Yards" FormatString="0.##" IsSelectAllTextOnFocus
                                      OnEnterAsync="@((val) => OnLotEnterAsync())"/>
                    </EditTemplate>
                </TableColumn>
                <TableColumn @bind-Field="@context.Location" Align="Alignment.Center">
                    <EditTemplate Context="v">
                        <BootstrapInput @bind-Value="@v.Location" FormatString="0.##" IsSelectAllTextOnFocus
                                        OnEnterAsync="@((val) => OnLotEnterAsync())" />
                    </EditTemplate>
                </TableColumn>
                <TableColumn @bind-Field="@context.Remark" Align="Alignment.Center">
                    <EditTemplate Context="v">
                        <BootstrapInput @bind-Value="@v.Remark" FormatString="0.##" IsSelectAllTextOnFocus
                                        OnEnterAsync="@((val) => OnLotEnterAsync())" />
                    </EditTemplate>
                </TableColumn>
            </TableColumns>
            <TableToolbarTemplate>
                @* <TableToolbarButton TItem="ProductInboundLot_View" Color="Color.Primary" Icon="fa-fw fa-solid fa-pen-to-square" Text="选择" OnClick="@OnSelectLotAsync" /> *@
            </TableToolbarTemplate>
            <RowButtonTemplate>
                <TableCellButton Size="Size.ExtraSmall" Icon="fa fa-info-circle" Color="Color.Info" Text="明细" OnClick="()=>OnDetailsClick(context)" />
            </RowButtonTemplate>

            <FooterTemplate>
                <TableFooterCell />
                <TableFooterCell Text="合计:" style="height: 36px;"/>
                <TableFooterCell Aggregate="AggregateType.Count" Field="@nameof(ProductInboundLot_View.LotNo)" />
                <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(ProductInboundLot_View.Pcs)" />
                <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(ProductInboundLot_View.Weight)" />
                <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(ProductInboundLot_View.Meters)" />
                <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(ProductInboundLot_View.Yards)" />
                <TableFooterCell Colspan="3" />
            </FooterTemplate>


        </Table>
    </div>
    <div class="@childTableClass">
        @*EnableKeyboardNavigationCell="true"在bb9.0以上版本有,但是并不好用,数字列中上下键依然只能+-1,无法移动焦点*@
        <Table @bind-Items="rollDetailList" TItem="ProductInboundRoll" 
               IsExcel="true" OnAddAsync="@OnAddRollAsync" OnSaveAsync="@OnSaveRollAsync" OnDeleteAsync="@OnDeleteRollAsync"
               ShowToolbar="true" ShowDeleteButton="true" ShowRefresh="false"
               ShowExtendButtons="false" IsTracking="true"
               ShowToastAfterSaveOrDeleteModel="false" TableSize="TableSize.Compact"  
               IsFixedHeader="true"  IsBordered="true"
               ShowFooter="true" IsFixedFooter="true" IsHideFooterWhenNoData="true">
            <TableColumns>
                <TableColumn @bind-Field="@context.RollNo" Align="Alignment.Center">
                    <EditTemplate Context="v">
                        <BootstrapInput @bind-Value="@v.RollNo" FormatString="0" IsSelectAllTextOnFocus
                                      OnEnterAsync="@((val) => OnRollEnterAsync())"/>
                    </EditTemplate>
                </TableColumn>
                <TableColumn @bind-Field="@context.Weight" Align="Alignment.Center">
                    <EditTemplate Context="v">
                        <BootstrapInput @bind-Value="@v.Weight" FormatString="0.##" IsSelectAllTextOnFocus
                                        OnEnterAsync="@((val) => OnRollEnterAsync())" />
                    </EditTemplate>
                </TableColumn>
                <TableColumn @bind-Field="@context.Meters" Align="Alignment.Center">
                    <EditTemplate Context="v">
                        <BootstrapInput @bind-Value="@v.Meters" FormatString="0.##" IsSelectAllTextOnFocus
                                      OnEnterAsync="@((val) => OnRollEnterAsync())"/>
                    </EditTemplate>
                </TableColumn>
                <TableColumn @bind-Field="@context.Yards" Align="Alignment.Center">
                    <EditTemplate Context="v">
                        <BootstrapInput @bind-Value="@v.Yards" FormatString="0.##" IsSelectAllTextOnFocus
                                      OnEnterAsync="@((val) => OnRollEnterAsync())"/>
                    </EditTemplate>
                </TableColumn>
                <TableColumn @bind-Field="@context.Remark" Align="Alignment.Center">
                    <EditTemplate Context="v">
                        <BootstrapInput @bind-Value="@v.Remark" FormatString="0.##" IsSelectAllTextOnFocus
                                        OnEnterAsync="@((val) => OnRollEnterAsync())" />
                    </EditTemplate>
                </TableColumn>
            </TableColumns>
            <TableToolbarTemplate>
                <TableToolbarButton Color="Color.Warning" Icon="fa fa-info-save" Text="保存" OnClick="@SaveRolls" />
            </TableToolbarTemplate> 
            <FooterTemplate>
                @* <TableFooterCell Text="合计:" style="height: 36px;" /> *@
                <TableFooterCell style="height: 36px;" Text="合计:" Aggregate="AggregateType.Count" Field="@nameof(ProductInboundRoll.RollNo)" />
                <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(ProductInboundRoll.Weight)" />
                <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(ProductInboundRoll.Meters)" />
                <TableFooterCell Aggregate="AggregateType.Sum" Field="@nameof(ProductInboundRoll.Yards)" />
                <TableFooterCell  />
            </FooterTemplate>
        </Table>

    </div>
</div>

<style>
    .parent-table-normal {
        width: 100%;
        display: inline-block;
    }

    .parent-table-compressed {
        width: 60%;
        display: inline-block;
        vertical-align: top;
    }

    .child-table-hidden {
        display: none;
    }

    .child-table-visible {
        width: 40%;
        display: inline-block;
        vertical-align: top;
    }

    .footer-demo hr {
        margin: 0;
    }

    .footer-demo tfoot tr,
    .footer-demo .table-row.table-footer .table-cell {
        color: #409eff;
        font-weight: bold;
    }
</style>

<script>
    // 输入缓存管理
    window.inputBuffer = {
        isWaitingForNewRow: false,
        bufferedInput: '',
        currentInput: null,

        // 开始等待新行
        startWaiting: function(input) {
            this.isWaitingForNewRow = true;
            this.bufferedInput = '';
            this.currentInput = input;

            // 暂时禁用当前输入框，防止继续输入
            if (input) {
                input.disabled = true;
                input.style.opacity = '0.6';
            }
        },

        // 结束等待，应用缓存的输入到新行
        endWaiting: function(newInput) {
            this.isWaitingForNewRow = false;

            // 恢复原输入框
            if (this.currentInput) {
                this.currentInput.disabled = false;
                this.currentInput.style.opacity = '1';
            }

            // 将缓存的输入应用到新输入框
            if (newInput && this.bufferedInput) {
                newInput.value = this.bufferedInput;
                // 触发input事件，确保Blazor绑定更新
                newInput.dispatchEvent(new Event('input', { bubbles: true }));
                newInput.dispatchEvent(new Event('change', { bubbles: true }));
            }

            this.bufferedInput = '';
            this.currentInput = null;
        },

        // 添加输入到缓存
        addToBuffer: function(char) {
            if (this.isWaitingForNewRow) {
                this.bufferedInput += char;
                return true; // 表示输入被缓存了
            }
            return false; // 表示正常处理输入
        }
    };

    // 全局键盘事件监听，捕获等待期间的输入
    document.addEventListener('keydown', function(e) {
        if (window.inputBuffer.isWaitingForNewRow) {
            // 只处理数字和小数点
            if ((e.key >= '0' && e.key <= '9') || e.key === '.') {
                e.preventDefault();
                window.inputBuffer.addToBuffer(e.key);
                return false;
            }
        }
    }, true);

    // Enter键导航到下一行 - 优化版本，解决快速输入问题
    window.navigateToNextRow = function (currentInput) {
        try {
            const td = currentInput.closest('td');
            const tr = td.parentNode;
            const nextRow = tr.nextElementSibling;

            if (nextRow) {
                // 获取当前单元格在行中的索引
                const cells = [...tr.children];
                const currentIndex = cells.indexOf(td);
                const nextRowCells = [...nextRow.children];

                // 在下一行的相同列位置查找输入框
                if (nextRowCells[currentIndex]) {
                    const nextInput = nextRowCells[currentIndex].querySelector('input');
                    if (nextInput && !nextInput.disabled && !nextInput.readOnly) {
                        nextInput.focus();
                        if (nextInput.select) {
                            nextInput.select();
                        }
                        return true;
                    }
                }
            }
            return false;
        } catch (error) {
            console.error('Error navigating to next row:', error);
            return false;
        }
    };

    // 等待新行添加完成后的焦点切换
    window.focusNewRowAndApplyBuffer = function() {
        try {
            // 查找当前活动元素所在的表格
            const activeElement = document.activeElement;
            if (!activeElement) return false;

            const table = activeElement.closest('table');
            if (!table) return false;

            const tbody = table.querySelector('tbody');
            if (!tbody) return false;

            // 获取最后一行（新添加的行）
            const lastRow = tbody.lastElementChild;
            if (!lastRow) return false;

            // 获取当前列索引
            const currentTd = activeElement.closest('td');
            const currentTr = currentTd.parentNode;
            const cells = [...currentTr.children];
            const currentIndex = cells.indexOf(currentTd);

            // 在新行的相同列位置查找输入框
            const newRowCells = [...lastRow.children];
            if (newRowCells[currentIndex]) {
                const newInput = newRowCells[currentIndex].querySelector('input');
                if (newInput && !newInput.disabled && !newInput.readOnly) {
                    newInput.focus();
                    if (newInput.select) {
                        newInput.select();
                    }

                    // 应用缓存的输入
                    window.inputBuffer.endWaiting(newInput);
                    return true;
                }
            }

            // 如果没有找到合适的输入框，也要结束等待状态
            window.inputBuffer.endWaiting(null);
            return false;
        } catch (error) {
            console.error('Error focusing new row:', error);
            window.inputBuffer.endWaiting(null);
            return false;
        }
    };
</script>
@code {

    [Parameter]
    public ProductInboundBill Bill { get; set; } = new();
    [Parameter]
    public EventCallback<ProductInboundBill> BillChanged { get; set; }

    [Inject]
    private WtmBlazorContext WtmBlazor { get; set; }

    [Inject]
    private IJSRuntime JSRuntime { get; set; }

    private List<SelectedItem> AllOrderDetails { get; set; } = new List<SelectedItem>();

    ProductInboundLot SelectedLot { get; set; }//存储当前选择的Lot
    private bool isAdd = false;
    //Lot子表绑定数据
    public IEnumerable<ProductInboundLot> DetailList
    {
        get { return Bill.LotList; }
        set
        {
            Bill.LotList = value.ToList();
        }
    }

    //Roll孙表绑定数据
    List<ProductInboundRoll> RollDetailList = new List<ProductInboundRoll>();
    public IEnumerable<ProductInboundRoll> rollDetailList
    {
        get { return RollDetailList; }
        set
        {
            RollDetailList = value.ToList();
        }
    }


    protected override async Task OnInitializedAsync()
    {
        if (Bill.LotList is null) Bill.LotList = new List<ProductInboundLot>();

        await base.OnInitializedAsync();
    }

    //当选择订单后,刷新订单明细选择数据源
    protected override async Task OnParametersSetAsync()
    {
        if (Bill.POrderId != Guid.Empty)
        {
            await SearchOrderDetailAsync(Bill.POrderId);
            isAdd = true;
        }
    }

    private async Task SearchOrderDetailAsync(Guid id)
    {
        var rv = await WtmBlazor.Api.CallAPI<List<SelectedItem>>($"/api/Models/OrderDetail/GetOrderDetailSelectListItemsByPurchaseOrderId/{id}");
        AllOrderDetails = rv.Data;
    }



    #region 更新Lot
    //Lot子表Excel模式,更新方法
    private async Task<ProductInboundLot> OnAddAsync()
    {
        var od = new ProductInboundLot();
        if (Bill.LotList is null) Bill.LotList = new();
        od.ID = Guid.NewGuid();
        Bill.LotList.Insert(Bill.LotList.Count(), od);
        //有async关键字就需要加await
        return await Task.FromResult(od);
        //return null;
    }

    private Task<bool> OnSaveAsync(ProductInboundLot item, ItemChangedType changedType)
    {
        Bill.Pcs = Bill.LotList.Count;
        Bill.Weight = Bill.LotList.Sum(x => x.Weight);
        Bill.Meters = Bill.LotList.Sum(x => x.Meters);
        Bill.Yards = Bill.LotList.Sum(x => x.Yards);
        return Task.FromResult(true);
    }

    private Task<bool> OnDeleteAsync(IEnumerable<ProductInboundLot> items)
    {
        Bill.LotList.RemoveAll(i => items.ToList().Contains(i));
        // 输出日志信息
        //Logger.Log($"集合值变化通知 列: {Items.Count} - 类型: Delete");
        return Task.FromResult(true);
    }
    #endregion


    #region 更新Roll
    //更新Roll
    private async Task<ProductInboundRoll> OnAddRollAsync()
    {
        var od = new ProductInboundRoll();
        if (RollDetailList is null) RollDetailList = new();
        od.ID = Guid.NewGuid();
        od.RollNo = RollDetailList.Count + 1;
        RollDetailList.Insert(RollDetailList.Count(), od);
        //有async关键字就需要加await
        return await Task.FromResult(od);
        //return null;
    }

    private Task<bool> OnSaveRollAsync(ProductInboundRoll item, ItemChangedType changedType)
    {
        // Bill.Pcs = RollDetailList.Count();
        // Bill.Weight = RollDetailList.Sum(x => x.Weight);
        // Bill.Meters = RollDetailList.Sum(x => x.Meters);
        // Bill.Yards = RollDetailList.Sum(x => x.Yards);
        return Task.FromResult(true);
    }

    private Task<bool> OnDeleteRollAsync(IEnumerable<ProductInboundRoll> items)
    {
        RollDetailList.RemoveAll(i => items.ToList().Contains(i));
        return Task.FromResult(true);
    }
    private string parentTableClass = "parent-table-normal";
    private string childTableClass = "child-table-hidden";
    //行明细按钮点击控制显示Roll表格
    private void OnDetailsClick(ProductInboundLot item)
    {
        // 检查是否点击的是同一行
        if (SelectedLot is not null && SelectedLot.ID == item.ID)
        {
            // 点击同一行时，切换显示状态
            parentTableClass = parentTableClass == "parent-table-compressed" ? "parent-table-normal" : "parent-table-compressed";
            childTableClass = childTableClass == "child-table-visible" ? "child-table-hidden" : "child-table-visible";
        }
        else
        {
            // 点击不同行时，显示新行的数据
            SelectedLot = item;
            RollDetailList = item.RollList ?? new List<ProductInboundRoll>();

            // 确保明细表格显示
            parentTableClass = "parent-table-compressed";
            childTableClass = "child-table-visible";
        }
        StateHasChanged();
    }

    //保存Roll
    private async Task SaveRolls()
    {
        var list = new List<ProductInboundRoll>();

        //去除空白Roll
        foreach (var item in RollDetailList)
        {
            if (item.Meters != 0 || item.Weight != 0 || item.Yards != 0)
            {
                list.Add(item);
            }
        }
        SelectedLot.RollList = list;
        SelectedLot.Color = AllOrderDetails.FirstOrDefault(x => x.Value == SelectedLot.OrderDetailId.ToString())?.Text;
        SelectedLot.Pcs = SelectedLot.RollList.Count;
        SelectedLot.Weight = SelectedLot.RollList.Sum(x => x.Weight);
        SelectedLot.Meters = SelectedLot.RollList.Sum(x => x.Meters);
        SelectedLot.Yards = SelectedLot.RollList.Sum(x => x.Yards);
        int index = Bill.LotList.FindIndex(x => x.ID == SelectedLot.ID);
        Bill.LotList[index] = SelectedLot;
        // 保存后隐藏明细表格
        parentTableClass = "parent-table-normal";
        childTableClass = "child-table-hidden";
        await Task.CompletedTask;
        //StateHasChanged(); //Click事件是EventCallback类型,本身会触发StateHasChanged，所以不需要再次触发
    }
    #endregion

    // Lot表格Enter键处理 - 优化版本，解决快速输入问题
    private async Task OnLotEnterAsync()
    {
        try
        {
            // 先尝试使用JavaScript导航到下一行
            var navigationResult = await JSRuntime.InvokeAsync<bool>("eval", @"
                (function() {
                    const activeElement = document.activeElement;
                    if (activeElement && activeElement.tagName === 'INPUT') {
                        return window.navigateToNextRow(activeElement);
                    }
                    return false;
                })();
            ");

            // 如果导航失败，说明需要添加新行
            if (!navigationResult)
            {
                // 开始等待新行，暂时禁用当前输入框并缓存后续输入
                await JSRuntime.InvokeVoidAsync("eval", @"
                    (function() {
                        const activeElement = document.activeElement;
                        if (activeElement && activeElement.tagName === 'INPUT') {
                            window.inputBuffer.startWaiting(activeElement);
                        }
                    })();
                ");

                await OnAddAsync(); // 添加新行
                StateHasChanged(); // 强制重新渲染
                await Task.Delay(100); // 等待DOM更新完成

                // 切换焦点到新行并应用缓存的输入
                await JSRuntime.InvokeVoidAsync("focusNewRowAndApplyBuffer");
            }
        }
        catch (Exception ex)
        {
            // 如果出现异常，确保清理等待状态
            System.Console.WriteLine($"Enter navigation failed: {ex.Message}");
            await JSRuntime.InvokeVoidAsync("eval", "window.inputBuffer.endWaiting(null);");
            await OnAddAsync();
            StateHasChanged();
        }
    }

    // Roll表格Enter键处理 - 优化版本，解决快速输入问题
    private async Task OnRollEnterAsync()
    {
        try
        {
            // 先尝试使用JavaScript导航到下一行
            var navigationResult = await JSRuntime.InvokeAsync<bool>("eval", @"
                (function() {
                    const activeElement = document.activeElement;
                    if (activeElement && activeElement.tagName === 'INPUT') {
                        return window.navigateToNextRow(activeElement);
                    }
                    return false;
                })();
            ");

            // 如果导航失败，说明需要添加新行
            if (!navigationResult)
            {
                // 开始等待新行，暂时禁用当前输入框并缓存后续输入
                await JSRuntime.InvokeVoidAsync("eval", @"
                    (function() {
                        const activeElement = document.activeElement;
                        if (activeElement && activeElement.tagName === 'INPUT') {
                            window.inputBuffer.startWaiting(activeElement);
                        }
                    })();
                ");

                await OnAddRollAsync(); // 添加新行
                StateHasChanged(); // 强制重新渲染
                await Task.Delay(150); // Roll表格等待时间稍长一些

                // 切换焦点到新行并应用缓存的输入
                await JSRuntime.InvokeVoidAsync("focusNewRowAndApplyBuffer");
            }
        }
        catch (Exception ex)
        {
            // 如果出现异常，确保清理等待状态
            System.Console.WriteLine($"Enter navigation failed: {ex.Message}");
            await JSRuntime.InvokeVoidAsync("eval", "window.inputBuffer.endWaiting(null);");
            await OnAddRollAsync();
            StateHasChanged();
        }
    }
}